<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test See More</title>
    <style>
        .category-box {
            width: 300px;
            height: 200px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            margin: 20px;
            position: relative;
        }
        
        .category-box.expanded {
            height: auto;
            min-height: 300px;
            background: #e0e0e0;
        }
        
        .category-content {
            display: none;
            padding: 15px;
        }
        
        .category-box.expanded .category-content {
            display: block;
        }
        
        .see-more {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 3px;
        }
        
        .category-box.expanded .see-more {
            position: static;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h1>Test See More Functionality</h1>
    
    <div class="category-box">
        <h3>Test Category</h3>
        <div class="category-content">
            <p>This is hidden content that should appear when expanded.</p>
            <ul>
                <li>Item 1</li>
                <li>Item 2</li>
                <li>Item 3</li>
            </ul>
        </div>
        <span class="see-more">Meer tonen</span>
    </div>

    <script>
        function initializeSeeMoreFunctionality() {
            const seeMoreButtons = document.querySelectorAll('.see-more');
            console.log('Found see-more buttons:', seeMoreButtons.length);
            
            seeMoreButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('See-more button clicked!');
                    
                    // Find the parent category-box
                    const categoryBox = this.closest('.category-box');
                    if (!categoryBox) {
                        console.log('No category-box found');
                        return;
                    }
                    
                    // Toggle the expanded state
                    categoryBox.classList.toggle('expanded');
                    console.log('Toggled expanded state, now:', categoryBox.classList.contains('expanded'));
                    
                    // Update button text
                    if (categoryBox.classList.contains('expanded')) {
                        this.textContent = 'Minder tonen';
                    } else {
                        this.textContent = 'Meer tonen';
                    }
                });
            });
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');
            initializeSeeMoreFunctionality();
        });
    </script>
</body>
</html>
