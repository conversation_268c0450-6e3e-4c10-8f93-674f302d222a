<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Test - Category Boxes</title>
    <link rel="stylesheet" href="main.css">
    <style>
        /* Force mobile view for testing */
        body {
            max-width: 400px;
            margin: 0 auto;
            background: #f0f0f0;
        }
        
        /* Debug borders */
        .category-box {
            border: 2px solid red !important;
        }
        
        .category-box img {
            border: 2px solid blue !important;
        }
        
        .category-box .see-more {
            border: 2px solid green !important;
        }
    </style>
</head>
<body>
    <h1>Mobile Category Box Test</h1>
    
    <div class="categories-section">
        <div class="see-categories">Populaire Supplementcategorieën</div>
        <div class="categories">
            <div class="category-box">
                <a href="./supplementen.html?category=Eiwitten" aria-label="Bekijk alle eiwitpoeder supplementen">
                    <img src="./Afbeeldingen/Whey.webp" alt="Categorie Eiwitten Supplementen - Whey protein en eiwitpoeders voor spieropbouw">
                    <span class="see-more">Eiwitsupplementen →</span>
                </a>
            </div>
            <div class="category-box">
                <a href="./supplementen.html?category=Creatine" aria-label="Bekijk alle creatine supplementen">
                    <img src="./Afbeeldingen/Creatine.webp" alt="Categorie Creatine Supplementen - Creatine monohydraat voor kracht en prestaties">
                    <span class="see-more">Beste creatine →</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        // Log viewport info
        console.log('Viewport width:', window.innerWidth);
        console.log('Viewport height:', window.innerHeight);
        
        // Log category box dimensions
        const boxes = document.querySelectorAll('.category-box');
        boxes.forEach((box, index) => {
            const rect = box.getBoundingClientRect();
            console.log(`Box ${index + 1}:`, {
                width: rect.width,
                height: rect.height,
                visible: rect.width > 0 && rect.height > 0
            });
        });
    </script>
</body>
</html>
